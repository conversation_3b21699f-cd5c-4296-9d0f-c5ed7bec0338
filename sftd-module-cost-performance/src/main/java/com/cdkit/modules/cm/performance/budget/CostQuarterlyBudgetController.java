package com.cdkit.modules.cm.performance.budget;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.api.budget.IQuarterlyBudgetApi;
import com.cdkit.modules.cm.api.budget.dto.AnnualRevenueRemainingBudgetDTO;
import com.cdkit.modules.cm.api.budget.dto.CostQuarterlyBudgetDTO;
import com.cdkit.modules.cm.api.budget.dto.CostQuarterlyBudgetMaterialDetailDTO;
import com.cdkit.modules.cm.api.budget.dto.CostQuarterlyBudgetRevenueDetailDTO;
import com.cdkit.modules.cm.api.budget.dto.ProcurementPackageSubjectDTO;
import com.cdkit.modules.cm.api.budget.dto.QuarterDateRangeDTO;
import com.cdkit.modules.cm.api.budget.dto.QuarterlyBudgetRealTimeCalculationRequestDTO;
import com.cdkit.modules.cm.api.budget.dto.QuarterlyBudgetRealTimeCalculationResponseDTO;
import com.cdkit.modules.cm.api.budget.dto.QuarterlyBudgetIndirectCostImportRequestDTO;
import com.cdkit.modules.cm.api.budget.dto.QuarterlyBudgetIndirectCostImportResponseDTO;
import org.springframework.web.multipart.MultipartFile;
import com.cdkit.modules.cm.api.project.dto.CostProjectPlanDTO;
import com.cdkit.modules.cm.application.budget.QuarterlyBudgetApplication;
import com.cdkit.modules.cm.application.project.CostProjectPlanApplicationService;
import com.cdkit.modules.cm.domain.budget.service.ProcurementPackageQueryService;
import com.cdkit.modules.cm.domain.project.mode.entity.CostMaterialDetailEntity;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetEntity;
import com.cdkit.modules.cm.domain.budget.service.QuarterTimeCalculationService;
import com.cdkit.modules.cm.domain.budget.service.RevenueDetailQueryService;
import com.cdkit.modules.cm.performance.budget.converter.AnnualRevenueRemainingBudgetConverter;
import com.cdkit.modules.cm.performance.budget.converter.CostQuarterlyBudgetConverter;
import com.cdkit.modules.cm.performance.budget.converter.CostQuarterlyBudgetRevenueDetailConverter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 季度预算控制器
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@RestController
@RequestMapping("/cm/costQuarterlyBudget")
@RequiredArgsConstructor
@Slf4j
public class CostQuarterlyBudgetController implements IQuarterlyBudgetApi {

    private final QuarterlyBudgetApplication quarterlyBudgetApplication;
    private final CostProjectPlanApplicationService costProjectPlanApplicationService;

    @Override
    public Result<IPage<CostQuarterlyBudgetDTO>> queryPageList(CostQuarterlyBudgetDTO queryVO, Integer pageNo, Integer pageSize) {
        log.info("开始分页查询季度预算列表，页码: {}, 每页数量: {}", pageNo, pageSize);

        try {
            // 转换查询条件
            CostQuarterlyBudgetEntity queryEntity = CostQuarterlyBudgetConverter.toEntity(queryVO);

            // 调用应用服务查询
            PageRes<CostQuarterlyBudgetEntity> pageResult = quarterlyBudgetApplication.queryPageList(queryEntity, pageNo, pageSize);

            // 转换结果
            List<CostQuarterlyBudgetDTO> dtoList = CostQuarterlyBudgetConverter.toDTOList(pageResult.getRecords());

            // 构建分页结果
            IPage<CostQuarterlyBudgetDTO> page = new Page<>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
            page.setRecords(dtoList);

            log.info("分页查询季度预算列表成功，总记录数: {}, 当前页记录数: {}", pageResult.getTotal(), dtoList.size());
            return Result.OK(page);

        } catch (Exception e) {
            log.error("分页查询季度预算列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<CostQuarterlyBudgetDTO> queryById(String id) {
        log.info("开始根据ID查询季度预算详情，ID: {}", id);

        try {
            CostQuarterlyBudgetEntity entity = quarterlyBudgetApplication.queryById(id);
            CostQuarterlyBudgetDTO dto = CostQuarterlyBudgetConverter.toDTO(entity);

            log.info("根据ID查询季度预算详情成功，ID: {}, 预算单号: {}", id, dto.getQuarterlyBudgetNo());
            return Result.OK(dto);

        } catch (Exception e) {
            log.error("根据ID查询季度预算详情失败，ID: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> add(CostQuarterlyBudgetDTO costQuarterlyBudget) {
        log.info("开始新增季度预算，预算单号: {}", costQuarterlyBudget.getQuarterlyBudgetNo());

        try {
            CostQuarterlyBudgetEntity entity = CostQuarterlyBudgetConverter.toEntity(costQuarterlyBudget);
            String id = quarterlyBudgetApplication.add(entity);

            log.info("新增季度预算成功，ID: {}, 预算单号: {}", id, costQuarterlyBudget.getQuarterlyBudgetNo());
            return Result.OK(id, "新增成功");

        } catch (Exception e) {
            log.error("新增季度预算失败，预算单号: {}", costQuarterlyBudget.getQuarterlyBudgetNo(), e);
            return Result.error("新增失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> edit(CostQuarterlyBudgetDTO costQuarterlyBudget) {
        log.info("开始编辑季度预算，ID: {}, 预算单号: {}", costQuarterlyBudget.getId(), costQuarterlyBudget.getQuarterlyBudgetNo());

        try {
            CostQuarterlyBudgetEntity entity = CostQuarterlyBudgetConverter.toEntity(costQuarterlyBudget);
            String id = quarterlyBudgetApplication.edit(entity);

            log.info("编辑季度预算成功，ID: {}, 预算单号: {}", id, costQuarterlyBudget.getQuarterlyBudgetNo());
            return Result.OK(id, "编辑成功");

        } catch (Exception e) {
            log.error("编辑季度预算失败，ID: {}, 预算单号: {}", costQuarterlyBudget.getId(), costQuarterlyBudget.getQuarterlyBudgetNo(), e);
            return Result.error("编辑失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> delete(String id) {
        log.info("开始删除季度预算，ID: {}", id);

        try {
            quarterlyBudgetApplication.delete(id);

            log.info("删除季度预算成功，ID: {}", id);
            return Result.OK("删除成功");

        } catch (Exception e) {
            log.error("删除季度预算失败，ID: {}", id, e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> deleteBatch(String ids) {
        log.info("开始批量删除季度预算，IDs: {}", ids);

        try {
            quarterlyBudgetApplication.deleteBatch(ids);

            log.info("批量删除季度预算成功，IDs: {}", ids);
            return Result.OK("批量删除成功");

        } catch (Exception e) {
            log.error("批量删除季度预算失败，IDs: {}", ids, e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }

    @Override
    public Result<String> generateNextQuarterlyBudgetNo() {
        log.info("开始生成下一个季度预算编号");

        try {
            String nextBudgetNo = quarterlyBudgetApplication.generateNextQuarterlyBudgetNo();

            log.info("生成下一个季度预算编号成功，编号: {}", nextBudgetNo);
            return Result.OK(nextBudgetNo, "生成成功");

        } catch (Exception e) {
            log.error("生成下一个季度预算编号失败", e);
            return Result.error("生成失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<String>> getQuarterOptions() {
        log.info("开始获取季度下拉框选项");

        try {
            List<String> quarterOptions = quarterlyBudgetApplication.getQuarterOptions();

            log.info("获取季度下拉框选项成功，共{}个选项", quarterOptions.size());
            return Result.OK(quarterOptions);

        } catch (Exception e) {
            log.error("获取季度下拉框选项失败", e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }

    @Override
    public Result<QuarterDateRangeDTO> getQuarterDateRange(String quarter) {
        log.info("开始计算季度日期范围，季度标识: {}", quarter);

        try {
            QuarterTimeCalculationService.QuarterDateRange dateRange =
                    quarterlyBudgetApplication.getQuarterDateRange(quarter);

            // 转换为DTO
            QuarterDateRangeDTO dto = new QuarterDateRangeDTO();
            dto.setQuarter(dateRange.getQuarter());
            dto.setStartDate(dateRange.getStartDate());
            dto.setEndDate(dateRange.getEndDate());

            log.info("计算季度日期范围成功，季度: {}, 开始日期: {}, 结束日期: {}",
                    quarter, dateRange.getStartDate(), dateRange.getEndDate());
            return Result.OK(dto);

        } catch (Exception e) {
            log.error("计算季度日期范围失败，季度标识: {}", quarter, e);
            return Result.error("计算失败：" + e.getMessage());
        }
    }

    /**
     * 根据季度计划的父计划ID查询对应的年度计划信息
     *
     * @param parentPlanId 父计划ID（季度计划关联的年度计划ID）
     * @return 年度计划详情，包含预估收入等信息
     */
    @Operation(summary = "根据父计划ID查询年度计划信息", description = "根据季度计划的parentPlanId字段查询对应的年度计划详情，特别包含预估收入金额字段")
    @GetMapping("/queryAnnualPlanByParentId")
    public Result<CostProjectPlanDTO> queryAnnualPlanByParentId(
            @Parameter(description = "父计划ID（年度计划ID）", required = true) @RequestParam String parentPlanId) {

        log.info("开始根据父计划ID查询年度计划信息，父计划ID: {}", parentPlanId);

        try {
            // 参数校验
            if (!StringUtils.hasText(parentPlanId)) {
                log.warn("父计划ID为空");
                return Result.error("父计划ID不能为空");
            }

            // 调用项目计划应用服务查询年度计划详情
            CostProjectPlanDTO annualPlan = costProjectPlanApplicationService.getById(parentPlanId);

            log.info("根据父计划ID查询年度计划信息成功，父计划ID: {}, 计划名称: {}, 预估收入: {}",
                    parentPlanId, annualPlan.getPlanName(), annualPlan.getContractRevenue());

            return Result.OK(annualPlan);

        } catch (Exception e) {
            log.error("根据父计划ID查询年度计划信息失败，父计划ID: {}", parentPlanId, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<ProcurementPackageSubjectDTO>> queryProcurementPackageSubjects(String quarterlyPlanId) {
        log.info("开始查询采办包预算科目信息，季度计划ID: {}", quarterlyPlanId);

        try {
            // 参数校验
            if (!StringUtils.hasText(quarterlyPlanId)) {
                log.warn("季度计划ID为空");
                return Result.error("季度计划ID不能为空");
            }

            // 调用应用层服务查询
            List<ProcurementPackageQueryService.ProcurementPackageSubjectInfo> subjectInfoList =
                quarterlyBudgetApplication.queryProcurementPackageSubjects(quarterlyPlanId);

            // 转换为DTO
            List<ProcurementPackageSubjectDTO> dtoList =
                CostQuarterlyBudgetConverter.toProcurementPackageSubjectDTOList(subjectInfoList);

            log.info("查询采办包预算科目信息成功，季度计划ID: {}, 查询到{}条记录",
                    quarterlyPlanId, dtoList.size());

            return Result.OK(dtoList);

        } catch (Exception e) {
            log.error("查询采办包预算科目信息失败，季度计划ID: {}", quarterlyPlanId, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }



    @Override
    public Result<List<CostQuarterlyBudgetMaterialDetailDTO>> queryMaterialDetailByQuarterlyPlanId(String quarterlyPlanId) {
        log.info("开始根据季度计划ID查询原材料明细，quarterlyPlanId: {}", quarterlyPlanId);

        try {
            // 参数校验
            if (!StringUtils.hasText(quarterlyPlanId)) {
                log.warn("季度计划ID为空");
                return Result.error("季度计划ID不能为空");
            }

            // 调用应用层服务查询原材料明细
            List<CostMaterialDetailEntity> materialDetailList =
                quarterlyBudgetApplication.queryMaterialDetailByQuarterlyPlanId(quarterlyPlanId);

            // 转换为DTO，字段与CostQuarterlyBudgetMaterialDetail保持一致
            List<CostQuarterlyBudgetMaterialDetailDTO> dtoList =
                CostQuarterlyBudgetConverter.toMaterialDetailDTOList(materialDetailList);

            log.info("根据季度计划ID查询原材料明细成功，quarterlyPlanId: {}, 查询到{}条记录",
                    quarterlyPlanId, dtoList.size());

            return Result.OK(dtoList);

        } catch (Exception e) {
            log.error("根据季度计划ID查询原材料明细失败，quarterlyPlanId: {}", quarterlyPlanId, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<CostQuarterlyBudgetRevenueDetailDTO>> queryRevenueDetailByQuarterlyPlanId(String quarterlyPlanId) {
        log.info("开始根据季度计划ID查询收入明细，quarterlyPlanId: {}", quarterlyPlanId);

        try {
            // 参数校验
            if (!StringUtils.hasText(quarterlyPlanId)) {
                log.warn("季度计划ID为空");
                return Result.error("季度计划ID不能为空");
            }

            // 调用应用层服务查询收入明细
            List<RevenueDetailQueryService.RevenueDetailInfo> revenueDetailList =
                quarterlyBudgetApplication.queryRevenueDetailByQuarterlyPlanId(quarterlyPlanId);

            // 转换为DTO，字段与CostQuarterlyBudgetRevenueDetail保持一致
            List<CostQuarterlyBudgetRevenueDetailDTO> dtoList =
                CostQuarterlyBudgetRevenueDetailConverter.toDTOList(revenueDetailList);

            log.info("根据季度计划ID查询收入明细成功，quarterlyPlanId: {}, 查询到{}条记录",
                    quarterlyPlanId, dtoList.size());

            return Result.OK(dtoList);

        } catch (Exception e) {
            log.error("根据季度计划ID查询收入明细失败，quarterlyPlanId: {}", quarterlyPlanId, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<AnnualRevenueRemainingBudgetDTO> queryAnnualRevenueRemainingBudget(String quarterlyPlanId) {
        log.info("开始查询年度收入剩余预算金额，quarterlyPlanId: {}", quarterlyPlanId);

        try {
            // 参数校验
            if (!StringUtils.hasText(quarterlyPlanId)) {
                log.warn("季度计划ID为空");
                return Result.error("季度计划ID不能为空");
            }

            // 调用应用层服务查询年度收入剩余预算金额
            var budgetInfo = quarterlyBudgetApplication.queryAnnualRevenueRemainingBudget(quarterlyPlanId);

            // 转换为DTO
            AnnualRevenueRemainingBudgetDTO dto = AnnualRevenueRemainingBudgetConverter.toDTO(budgetInfo);

            log.info("查询年度收入剩余预算金额成功，quarterlyPlanId: {}, 剩余预算金额: {}元",
                    quarterlyPlanId, dto.getAnnualRevenueRemainingBudgetAmount());

            return Result.OK(dto);

        } catch (Exception e) {
            log.error("查询年度收入剩余预算金额失败，quarterlyPlanId: {}", quarterlyPlanId, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<QuarterlyBudgetRealTimeCalculationResponseDTO> realTimeCalculation(QuarterlyBudgetRealTimeCalculationRequestDTO request) {
        log.info("开始季度预算实时计算，季度计划ID: {}, 年度预算ID: {}",
                request.getQuarterlyPlanId(), request.getAnnualBudgetId());

        try {
            // 参数校验
            if (!StringUtils.hasText(request.getQuarterlyPlanId())) {
                log.warn("季度计划ID为空");
                return Result.error("季度计划ID不能为空");
            }
            if (!StringUtils.hasText(request.getAnnualBudgetId())) {
                log.warn("年度预算ID为空");
                return Result.error("年度预算ID不能为空");
            }

            // 调用应用层服务进行实时计算
            QuarterlyBudgetRealTimeCalculationResponseDTO response =
                quarterlyBudgetApplication.realTimeCalculation(request);

            log.info("季度预算实时计算成功，季度计划ID: {}, 项目支出预算总额: {}元, 间接费预算总额: {}元",
                    request.getQuarterlyPlanId(),
                    response.getProjectExpenditureBudgetTotal(),
                    response.getIndirectCostBudgetTotal());

            return Result.OK(response);

        } catch (Exception e) {
            log.error("季度预算实时计算失败，季度计划ID: {}, 年度预算ID: {}",
                    request.getQuarterlyPlanId(), request.getAnnualBudgetId(), e);
            return Result.error("实时计算失败：" + e.getMessage());
        }
    }

    @Override
    public Result<QuarterlyBudgetIndirectCostImportResponseDTO> importIndirectCost(
            String quarterlyPlanId, String annualBudgetId,
            QuarterlyBudgetIndirectCostImportRequestDTO.IndirectCostType indirectCostType,
            MultipartFile file) {
        log.info("开始季度预算间接成本导入，季度计划ID: {}, 年度预算ID: {}, 间接成本类型: {}",
                quarterlyPlanId, annualBudgetId, indirectCostType);

        try {
            // 参数校验
            if (!StringUtils.hasText(quarterlyPlanId)) {
                log.warn("季度计划ID为空");
                return Result.error("季度计划ID不能为空");
            }
            if (!StringUtils.hasText(annualBudgetId)) {
                log.warn("年度预算ID为空");
                return Result.error("年度预算ID不能为空");
            }
            if (indirectCostType == null) {
                log.warn("间接成本类型为空");
                return Result.error("间接成本类型不能为空");
            }
            if (file == null || file.isEmpty()) {
                log.warn("Excel文件为空");
                return Result.error("Excel文件不能为空");
            }

            // 构建请求对象
            QuarterlyBudgetIndirectCostImportRequestDTO request = new QuarterlyBudgetIndirectCostImportRequestDTO();
            request.setQuarterlyPlanId(quarterlyPlanId);
            request.setAnnualBudgetId(annualBudgetId);
            request.setIndirectCostType(indirectCostType);
            request.setFile(file);

            // 调用应用层服务进行导入
            QuarterlyBudgetIndirectCostImportResponseDTO response =
                quarterlyBudgetApplication.importIndirectCost(request);

            log.info("季度预算间接成本导入成功，季度计划ID: {}, 成功: {}条, 失败: {}条",
                    quarterlyPlanId,
                    response.getSuccessList() != null ? response.getSuccessList().size() : 0,
                    response.getFailureList() != null ? response.getFailureList().size() : 0);

            return Result.OK(response);

        } catch (Exception e) {
            log.error("季度预算间接成本导入失败，季度计划ID: {}, 年度预算ID: {}",
                    quarterlyPlanId, annualBudgetId, e);
            return Result.error("间接成本导入失败：" + e.getMessage());
        }
    }

    // ==================== 详情查询接口 ====================

    /**
     * 根据季度预算ID查询采办包明细
     *
     * @param id 季度预算ID
     * @return 采办包明细列表
     */
    @Operation(summary = "根据季度预算ID查询采办包明细", description = "根据季度预算主表ID查询对应的采办包明细数据")
    @GetMapping("/queryProcPkgDetailByMainId")
    public Result<List<CostQuarterlyBudgetDTO.CostQuarterlyBudgetProcPkgDetailDTO>> queryProcPkgDetailByMainId(
            @Parameter(description = "季度预算ID", required = true) @RequestParam String id) {

        log.info("开始根据季度预算ID查询采办包明细，ID: {}", id);

        try {
            // 参数校验
            if (!StringUtils.hasText(id)) {
                log.warn("季度预算ID为空");
                return Result.error("季度预算ID不能为空");
            }

            // 调用应用层服务查询
            var detailList = quarterlyBudgetApplication.queryProcPkgDetailByMainId(id);

            // 转换为DTO
            var dtoList = CostQuarterlyBudgetConverter.toProcPkgDetailDTOListFromDomain(detailList);

            log.info("根据季度预算ID查询采办包明细成功，ID: {}, 查询到{}条记录", id, dtoList.size());
            return Result.OK(dtoList);

        } catch (Exception e) {
            log.error("根据季度预算ID查询采办包明细失败，ID: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据季度预算ID查询原材料明细
     *
     * @param id 季度预算ID
     * @return 原材料明细列表
     */
    @Operation(summary = "根据季度预算ID查询原材料明细", description = "根据季度预算主表ID查询对应的原材料明细数据")
    @GetMapping("/queryMaterialDetailByMainId")
    public Result<List<CostQuarterlyBudgetDTO.CostQuarterlyBudgetMaterialDetailDTO>> queryMaterialDetailByMainId(
            @Parameter(description = "季度预算ID", required = true) @RequestParam String id) {

        log.info("开始根据季度预算ID查询原材料明细，ID: {}", id);

        try {
            // 参数校验
            if (!StringUtils.hasText(id)) {
                log.warn("季度预算ID为空");
                return Result.error("季度预算ID不能为空");
            }

            // 调用应用层服务查询
            var detailList = quarterlyBudgetApplication.queryMaterialDetailByMainId(id);

            // 转换为DTO
            var dtoList = CostQuarterlyBudgetConverter.toMaterialDetailDTOListFromDomain(detailList);

            log.info("根据季度预算ID查询原材料明细成功，ID: {}, 查询到{}条记录", id, dtoList.size());
            return Result.OK(dtoList);

        } catch (Exception e) {
            log.error("根据季度预算ID查询原材料明细失败，ID: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据季度预算ID查询预算科目明细直接成本
     *
     * @param id 季度预算ID
     * @return 预算科目明细直接成本列表
     */
    @Operation(summary = "根据季度预算ID查询预算科目明细直接成本", description = "根据季度预算主表ID查询对应的预算科目明细直接成本数据")
    @GetMapping("/querySubjectDirectCostByMainId")
    public Result<List<CostQuarterlyBudgetDTO.CostQuarterlyBudgetSubjectDirectCostDTO>> querySubjectDirectCostByMainId(
            @Parameter(description = "季度预算ID", required = true) @RequestParam String id) {

        log.info("开始根据季度预算ID查询预算科目明细直接成本，ID: {}", id);

        try {
            // 参数校验
            if (!StringUtils.hasText(id)) {
                log.warn("季度预算ID为空");
                return Result.error("季度预算ID不能为空");
            }

            // 调用应用层服务查询
            var detailList = quarterlyBudgetApplication.querySubjectDirectCostByMainId(id);

            // 转换为DTO
            var dtoList = CostQuarterlyBudgetConverter.toSubjectDirectCostDTOListFromDomain(detailList);

            log.info("根据季度预算ID查询预算科目明细直接成本成功，ID: {}, 查询到{}条记录", id, dtoList.size());
            return Result.OK(dtoList);

        } catch (Exception e) {
            log.error("根据季度预算ID查询预算科目明细直接成本失败，ID: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据季度预算ID查询本中心间接成本
     *
     * @param id 季度预算ID
     * @return 本中心间接成本列表
     */
    @Operation(summary = "根据季度预算ID查询本中心间接成本", description = "根据季度预算主表ID查询对应的本中心间接成本数据")
    @GetMapping("/queryCenterIndirectCostByMainId")
    public Result<List<CostQuarterlyBudgetDTO.CostQuarterlyBudgetCenterIndirectCostDTO>> queryCenterIndirectCostByMainId(
            @Parameter(description = "季度预算ID", required = true) @RequestParam String id) {

        log.info("开始根据季度预算ID查询本中心间接成本，ID: {}", id);

        try {
            // 参数校验
            if (!StringUtils.hasText(id)) {
                log.warn("季度预算ID为空");
                return Result.error("季度预算ID不能为空");
            }

            // 调用应用层服务查询
            var detailList = quarterlyBudgetApplication.queryCenterIndirectCostByMainId(id);

            // 转换为DTO
            var dtoList = CostQuarterlyBudgetConverter.toCenterIndirectCostDTOListFromDomain(detailList);

            log.info("根据季度预算ID查询本中心间接成本成功，ID: {}, 查询到{}条记录", id, dtoList.size());
            return Result.OK(dtoList);

        } catch (Exception e) {
            log.error("根据季度预算ID查询本中心间接成本失败，ID: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据季度预算ID查询综合管理间接成本
     *
     * @param id 季度预算ID
     * @return 综合管理间接成本列表
     */
    @Operation(summary = "根据季度预算ID查询综合管理间接成本", description = "根据季度预算主表ID查询对应的综合管理间接成本数据")
    @GetMapping("/queryCompMageIndirectCostByMainId")
    public Result<List<CostQuarterlyBudgetDTO.CostQuarterlyBudgetCompMageIndirectCostDTO>> queryCompMageIndirectCostByMainId(
            @Parameter(description = "季度预算ID", required = true) @RequestParam String id) {

        log.info("开始根据季度预算ID查询综合管理间接成本，ID: {}", id);

        try {
            // 参数校验
            if (!StringUtils.hasText(id)) {
                log.warn("季度预算ID为空");
                return Result.error("季度预算ID不能为空");
            }

            // 调用应用层服务查询
            var detailList = quarterlyBudgetApplication.queryCompMageIndirectCostByMainId(id);

            // 转换为DTO
            var dtoList = CostQuarterlyBudgetConverter.toCompMageIndirectCostDTOListFromDomain(detailList);

            log.info("根据季度预算ID查询综合管理间接成本成功，ID: {}, 查询到{}条记录", id, dtoList.size());
            return Result.OK(dtoList);

        } catch (Exception e) {
            log.error("根据季度预算ID查询综合管理间接成本失败，ID: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据季度预算ID查询非经营中心间接成本
     *
     * @param id 季度预算ID
     * @return 非经营中心间接成本列表
     */
    @Operation(summary = "根据季度预算ID查询非经营中心间接成本", description = "根据季度预算主表ID查询对应的非经营中心间接成本数据")
    @GetMapping("/queryNonOptCenterIndirectCostByMainId")
    public Result<List<CostQuarterlyBudgetDTO.CostQuarterlyBudgetNonOptCenterIndirectCostDTO>> queryNonOptCenterIndirectCostByMainId(
            @Parameter(description = "季度预算ID", required = true) @RequestParam String id) {

        log.info("开始根据季度预算ID查询非经营中心间接成本，ID: {}", id);

        try {
            // 参数校验
            if (!StringUtils.hasText(id)) {
                log.warn("季度预算ID为空");
                return Result.error("季度预算ID不能为空");
            }

            // 调用应用层服务查询
            var detailList = quarterlyBudgetApplication.queryNonOptCenterIndirectCostByMainId(id);

            // 转换为DTO
            var dtoList = CostQuarterlyBudgetConverter.toNonOptCenterIndirectCostDTOListFromDomain(detailList);

            log.info("根据季度预算ID查询非经营中心间接成本成功，ID: {}, 查询到{}条记录", id, dtoList.size());
            return Result.OK(dtoList);

        } catch (Exception e) {
            log.error("根据季度预算ID查询非经营中心间接成本失败，ID: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据季度预算ID查询收入明细
     *
     * @param id 季度预算ID
     * @return 收入明细列表
     */
    @Operation(summary = "根据季度预算ID查询收入明细", description = "根据季度预算主表ID查询对应的收入明细数据")
    @GetMapping("/queryRevenueDetailByMainId")
    public Result<List<CostQuarterlyBudgetDTO.CostQuarterlyBudgetRevenueDetailDTO>> queryRevenueDetailByMainId(
            @Parameter(description = "季度预算ID", required = true) @RequestParam String id) {

        log.info("开始根据季度预算ID查询收入明细，ID: {}", id);

        try {
            // 参数校验
            if (!StringUtils.hasText(id)) {
                log.warn("季度预算ID为空");
                return Result.error("季度预算ID不能为空");
            }

            // 调用应用层服务查询
            var detailList = quarterlyBudgetApplication.queryRevenueDetailByMainId(id);

            // 转换为DTO
            var dtoList = CostQuarterlyBudgetConverter.toRevenueDetailDTOListFromDomain(detailList);

            log.info("根据季度预算ID查询收入明细成功，ID: {}, 查询到{}条记录", id, dtoList.size());
            return Result.OK(dtoList);

        } catch (Exception e) {
            log.error("根据季度预算ID查询收入明细失败，ID: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

}
